/*
 * 太阳能逆变器主控制头文件
 * 定义了太阳能逆变器系统的主要数据结构、函数声明和宏定义
 * 包含MPPT控制、并网同步、保护功能等核心模块
 */

#ifndef __SOLAR_H    // 防止头文件重复包含的宏定义开始
#define __SOLAR_H    // 定义头文件标识符

// 包含系统通用头文件，提供基础数据类型和常用函数
#include "common.h"

// 包含采样模块头文件，用于电压电流信号采集
#include <Sample.h>

// #include "AdamLib.h"  // 注释掉的Adam库头文件（可能是调试库）
#include "SCI.h"        // 串行通信接口头文件，用于数据通信
#include "protect.h"    // 保护功能头文件，包含过压过流等保护机制
#include "pvboost.h"    // 光伏升压模块头文件，用于DC-DC升压控制

#include "comm.h"       // 通信模块头文件，处理外部通信协议
#include "dq_svpwm.h"   // DQ坐标变换和空间矢量PWM头文件
#include "cntl.h"       // 控制算法头文件，包含PI控制器等

// 调试功能开关宏定义：1表示开启调试功能，0表示关闭
#define SOLAR_DEBUG 1

// 当调试功能开启时，定义调试相关的数据结构
#if SOLAR_DEBUG
typedef struct debug_handle_t
{
    float cntl_value;           // 控制值，用于调试监控

    float data_buff[20][100];   // 数据缓冲区，存储20组数据，每组100个浮点数

    float dlog_buff[4][100];    // 数据记录缓冲区，4通道，每通道100个数据点
    float dlog_get[4];          // 数据记录获取数组，存储4个通道的当前值
    DLOG_4CH_F hdlog1;          // 4通道数据记录器句柄

    // 电网模拟相关变量（用于测试和仿真）
    DQ0_ABC_F simulation_grid;     // 模拟电网电压的DQ0到ABC坐标变换结构
    DQ0_ABC_F simulation_current;  // 模拟电流的DQ0到ABC坐标变换结构
    float simulation_theta;        // 模拟电网相位角（弧度）
    float simulation_freq;         // 模拟电网频率（Hz）
    int simulation_sw;             // 模拟开关标志位

    // 定时器计数相关变量（用于性能监控）
    uint32_t TIM2_time_count;      // 定时器2计数值
    uint32_t TIM0_time_count;      // 定时器0计数值
    float time2_count, time2_count_max;  // 定时器2当前计数和最大计数
    float time0_count, time0_count_max;  // 定时器0当前计数和最大计数
} debug_handle_t;
extern debug_handle_t hdebug;  // 声明全局调试句柄变量
#endif

/*
 * 太阳能逆变器主控制句柄结构体
 * 包含了逆变器运行所需的所有控制参数、状态变量和功能模块
 */
typedef struct _solar_handle
{
    // 电流测量相关变量
    float meter_Ia, meter_Ib, meter_Ic, meter_LC;  // A相、B相、C相电流测量值和LC滤波器电流
    float Ia_gain, Ib_gain, Ic_gain, LC_gain;      // 各相电流的增益系数，用于校准

    // DQ坐标系下的输出控制量
    float Out_d;                // D轴输出控制量（有功分量）
    float Out_q;                // Q轴输出控制量（无功分量）
    dq_svpwm_t hDQ_SVPWM;      // DQ坐标到空间矢量PWM变换的控制句柄

    // 坐标变换相关句柄（用于三相交流到DQ坐标系的转换）
    ABC_DQ0_POS_F hABC_DQ0_POS;    // ABC到DQ0正序坐标变换句柄
    ABC_DQ0_NEG_F hABC_DQ0_NEG;    // ABC到DQ0负序坐标变换句柄
    DQ0_ABC_F hV_DQ0_ABC;          // DQ0到ABC坐标变换句柄（电压）

    // 软件锁相环（SPLL）相关变量，用于电网同步
    SPLL_3ph_DDSRF_F hSPLL_3ph_DDSRF;     // 三相双同步参考坐标系锁相环句柄
    float spll_theta;                      // 锁相环输出相位角 [0,2π弧度]
    float spll_freq;                       // 锁相环检测到的电网频率 [Hz]
    float spll_theta_compensation;         // 相位补偿角度，用于系统延迟补偿
    int spll_phase_sequence;               // 电网相序检测：1表示正序，0表示负序

    // 电流的坐标变换和锁相环
    ABC_DQ0_POS_F hIabc_dq0_pos;          // 电流ABC到DQ0正序变换句柄
    ABC_DQ0_NEG_F hIabc_dq0_neg;          // 电流ABC到DQ0负序变换句柄
    SPLL_3ph_DDSRF_F hIspll_3ph_ddsrf;    // 电流锁相环句柄
    float Id_lpf;                          // D轴电流低通滤波后的值
    float Iq_lpf;                          // Q轴电流低通滤波后的值

    /*
     * 功率计算说明：
     * 有功功率计算公式：P = Vd * Id + Vq * Iq
     * 无功功率计算公式：Q = Vq * Id - Vd * Iq
     * 其中：Vd,Vq为电压的DQ分量；Id,Iq为电流的DQ分量
     */

    // MPPT（最大功率点跟踪）算法相关句柄
    MPPT_PNO_F hMPPT_PNO1;           // 第一路光伏的扰动观察法MPPT句柄
    MPPT_PNO_F hMPPT_PNO2;           // 第二路光伏的扰动观察法MPPT句柄
    MPPT_INCC_F hMPPT_INCC1;         // 第一路光伏的增量电导法MPPT句柄
    MPPT_INCC_F hMPPT_INCC2;         // 第二路光伏的增量电导法MPPT句柄
    MPPT_INCC_I_F hMPPT_INCC_I_1;    // 第一路光伏的改进增量电导法MPPT句柄
    MPPT_INCC_I_F hMPPT_INCC_I_2;    // 第二路光伏的改进增量电导法MPPT句柄
    float Vpvref_mpptOut1;            // 第一路MPPT输出的光伏电压参考值
    float Vpvref_mpptOut2;            // 第二路MPPT输出的光伏电压参考值

    // PI控制器句柄集合，用于各种闭环控制
    CNTL_PI_F hCNTL_PI_Vpv1;           // 第一路光伏电压PI控制器句柄
    CNTL_PI_F hCNTL_PI_Vpv2;           // 第二路光伏电压PI控制器句柄
    CNTL_PI_F hCNTL_PI_Vbus;           // 直流母线电压PI控制器句柄
    CNTL_PI_F hCNTL_PI_BusBalance;     // 直流母线平衡控制PI控制器句柄

    CNTL_PI_F hCNTL_PI_d;              // D轴电流PI控制器句柄（有功电流控制）
    CNTL_PI_F hCNTL_PI_q;              // Q轴电流PI控制器句柄（无功电流控制）

    // 直流母线平衡控制相关变量
    int16_t cntl_bus_balance_compensation;  // 母线平衡补偿值（取值范围：1或-1）
    int16_t cntl_bus_balance_table[400];    // 母线平衡查找表，存储400个补偿值

    // 系统控制状态标志位
    int is_output_epwm_enable;         // PWM输出使能标志：1表示允许输出，0表示禁止
    //    int is_interconnection;      // 并网状态标志（已注释）
    int is_amp_sync_ok;                // 幅值同步完成标志：1表示同步完成，是并网的前提条件
    int Ctrl_Power_SW;                 // 功率控制开关标志

    // 功能模块句柄
    Protect_Handle_t hProtect;         // 保护功能模块句柄，处理各种故障保护
    PVBoost_Handle_t hBoost;           // 光伏升压模块句柄，控制DC-DC升压电路

    // 交流信号采样句柄集合（用于电压和电流的实时采集）
    Sample_AC_HandleTypeDef Vgrid_a;      // 电网A相电压采样句柄
    Sample_AC_HandleTypeDef Vgrid_b;      // 电网B相电压采样句柄
    Sample_AC_HandleTypeDef Vgrid_c;      // 电网C相电压采样句柄
    Sample_AC_HandleTypeDef Vinv_a;       // 逆变器A相输出电压采样句柄
    Sample_AC_HandleTypeDef Vinv_b;       // 逆变器B相输出电压采样句柄
    Sample_AC_HandleTypeDef Vinv_c;       // 逆变器C相输出电压采样句柄
    Sample_AC_HandleTypeDef Ia;           // A相输出电流采样句柄
    Sample_AC_HandleTypeDef Ib;           // B相输出电流采样句柄
    Sample_AC_HandleTypeDef Ic;           // C相输出电流采样句柄

    // 直流信号采样句柄集合（用于光伏和直流母线的采集）
    Sample_DC_HandleTypeDef Ipv1;         // 第一路光伏电流采样句柄
    Sample_DC_HandleTypeDef Ipv2;         // 第二路光伏电流采样句柄
    Sample_DC_HandleTypeDef Vpv1;         // 第一路光伏电压采样句柄
    Sample_DC_HandleTypeDef Vpv2;         // 第二路光伏电压采样句柄
    Sample_DC_HandleTypeDef Vbus_pos;     // 直流母线正极电压采样句柄
    Sample_DC_HandleTypeDef Vbus_neg;     // 直流母线负极电压采样句柄

    // 滤波后的直流量（经过低通滤波处理，去除高频噪声）
    float Vbus;                           // 直流母线总电压（正极电压+负极电压）
    float Vpv1_lpf;                       // 第一路光伏电压低通滤波值
    float Vpv2_lpf;                       // 第二路光伏电压低通滤波值
    float Ipv1_lpf;                       // 第一路光伏电流低通滤波值
    float Ipv2_lpf;                       // 第二路光伏电流低通滤波值

    // 输入功率计算（光伏板产生的功率）
    float Input_Power_MPPT1;             // 第一路光伏板实时功率（瓦特）
    float Input_Power_MPPT2;             // 第二路光伏板实时功率（瓦特）
    float Input_Power_Total;             // 光伏板总输入功率（MPPT1 + MPPT2）

    // 输出功率计算（逆变器输出到电网的功率）
    float Output_Power_a_rms_xp;         // A相逆变输出功率（有效值计算）
    float Output_Power_b_rms_xp;         // B相逆变输出功率（有效值计算）
    float Output_Power_c_rms_xp;         // C相逆变输出功率（有效值计算）
    float Output_Power_Total_rms_xp;     // 三相逆变总输出功率

    // 一级保护成员（基本保护功能）
    Protect_Member_t Protect_Ia;         // A相电流保护成员（过流、欠流检测）
    Protect_Member_t Protect_Ib;         // B相电流保护成员
    Protect_Member_t Protect_Ic;         // C相电流保护成员

    Protect_Member_t Protect_Ipv1;       // 第一路光伏电流保护成员
    Protect_Member_t Protect_Ipv2;       // 第二路光伏电流保护成员

    // 二级保护成员（更严格的保护标准）
    Protect_Member_t Protect_Ia_II;      // A相电流二级保护成员
    Protect_Member_t Protect_Ib_II;      // B相电流二级保护成员
    Protect_Member_t Protect_Ic_II;      // C相电流二级保护成员

    Protect_Member_t Protect_Ipv1_II;    // 第一路光伏电流二级保护成员
    Protect_Member_t Protect_Ipv2_II;    // 第二路光伏电流二级保护成员

    // 电网参数保护成员
    Protect_Member_t Protect_Grid_Freq;  // 电网频率保护成员（频率异常检测）
    Protect_Member_t Protect_Grid_Ua;    // 电网A相电压保护成员（过压欠压检测）
    Protect_Member_t Protect_Grid_Ub;    // 电网B相电压保护成员
    Protect_Member_t Protect_Grid_Uc;    // 电网C相电压保护成员

    // 光伏电压保护成员
    Protect_Member_t Protect_Vpv1;       // 第一路光伏电压保护成员
    Protect_Member_t Protect_Vpv2;       // 第二路光伏电压保护成员

    // 直流母线保护成员
    Protect_Member_t Protect_VBusBalance; // 直流母线平衡保护成员（正负母线电压差检测）
    Protect_Member_t Protect_VBusSum;     // 直流母线总电压保护成员（过压欠压检测）

    // 消息队列系统（用于系统状态和故障信息管理）
    const char *message_list[10];        // 消息队列数组，最多存储10条消息
    uint16_t message_head;               // 消息队列头指针，指向下一个要读取的消息
    uint16_t message_count;              // 当前消息队列中的消息数量

    /*
     * 太阳能逆变器状态机枚举
     * 定义了逆变器从启动到正常运行的各个状态
     */
    enum _solar_state
    {
        HSTATE_STOP,      // 停机状态：系统关闭，所有输出禁止
        HSTATE_START,     // 启动状态：系统初始化，检查各项参数
        HSTATE_SYNC,      // 同步状态：与电网进行频率和相位同步
        HSTATE_MERGE_ING, // 并网准备状态：同步完成，准备并网
        HSTATE_RUNING,    // 运行状态：正常并网发电运行
    } state;              // 当前系统状态变量

    /*
     * 太阳能逆变器数据结构
     * 包含了所有需要对外通信或显示的运行数据
     */
    struct solar_data_t
    {
        uint32_t data_head;              // 数据包头标识，用于数据包完整性校验
        uint32_t solar_state;            // 当前系统状态（对应上面的状态机）

        // 发电量统计数据
        float Total_EG;                  // 累计发电量（千瓦时）
        float Today_EG;                  // 今日发电量（千瓦时）
        float active_power;              // 当前有功功率输出（瓦特）
        float apparent_power;            // 当前视在功率（伏安）
        float Pf;                        // 功率因数（有功功率/视在功率）

        // 光伏输入参数
        float Vpv1;                      // 第一路光伏电压（伏特）
        float Ipv1;                      // 第一路光伏电流（安培）
        float Vpv2;                      // 第二路光伏电压（伏特）
        float Ipv2;                      // 第二路光伏电流（安培）

        // 直流母线参数
        float VBus;                      // 直流母线总电压（伏特）
        float VBusPos;                   // 直流母线正极电压（伏特）
        float VBusNeg;                   // 直流母线负极电压（伏特）

        // 电网电压参数
        float GVa;                       // 电网A相电压有效值（伏特）
        float GVb;                       // 电网B相电压有效值（伏特）
        float GVc;                       // 电网C相电压有效值（伏特）

        // 输出电流参数
        float Ia;                        // A相输出电流有效值（安培）
        float Ib;                        // B相输出电流有效值（安培）
        float Ic;                        // C相输出电流有效值（安培）

        float Freq;                      // 电网频率（赫兹）

        // PWM控制参数
        uint32_t boost1_pwm;             // 第一路升压电路PWM占空比值
        uint32_t boost2_pwm;             // 第二路升压电路PWM占空比值

        // MPPT输出参考值
        float Vpvref_mpptOut1;           // 第一路MPPT算法输出的光伏电压参考值
        float Vpvref_mpptOut2;           // 第二路MPPT算法输出的光伏电压参考值

        // 功率数据（用于监控和显示）
        float Input_Power_MPPT1;         // 第一路光伏板输入功率（瓦特）
        float Input_Power_MPPT2;         // 第二路光伏板输入功率（瓦特）
        float Input_Power_Total;         // 光伏板总输入功率（瓦特）

        float Output_Power_a;            // A相逆变输出功率（瓦特）
        float Output_Power_b;            // B相逆变输出功率（瓦特）
        float Output_Power_c;            // C相逆变输出功率（瓦特）
        float Output_Power_T;            // 三相逆变总输出功率（瓦特）

        // 系统性能监控
        float tim_program_elapsed_time;  // 程序执行时间统计（微秒），用于性能分析
        uint32_t protect_word[2];        // 保护状态字，记录各种保护功能的触发状态
    } data;                              // 数据结构实例

    uint32_t time_tick;                  // 系统时间计数器，用于时间戳和定时功能

} solar_handle_t;  // 太阳能逆变器主控制句柄类型定义结束

// 全局变量声明
extern solar_handle_t h;                // 全局太阳能逆变器控制句柄实例
// extern solar_handle_t *hsolar;       // 指针方式的句柄（已注释）

// 时间相关宏定义
#define SOLAR_GET_TICK() (h.time_tick)                           // 获取当前系统时间计数
#define SOLAR_GET_TICK_COUNT(start_tick) (h.time_tick - start_tick)  // 计算从起始时间到现在的时间差

/*
 * ========== 中断服务函数声明 ==========
 */

/**
 * @brief 1毫秒定时中断服务函数
 * @param 无
 * @return 无
 * @note 用于执行1ms周期的控制任务，如状态管理、通信处理等
 */
void solar_ISR_1ms(void);

/**
 * @brief 50微秒定时中断服务函数
 * @param 无
 * @return 无
 * @note 用于执行高频控制任务，如PWM更新、电流环控制等
 */
void solar_ISR_50us(void);

/*
 * ========== 主要功能函数声明 ==========
 */

/**
 * @brief 太阳能逆变器主循环函数
 * @param 无
 * @return 无
 * @note 在主程序中循环调用，处理非实时任务
 */
void solar_Loop(void);

/**
 * @brief 太阳能逆变器系统初始化函数
 * @param 无
 * @return 无
 * @note 初始化所有控制参数、硬件配置和数据结构
 */
void solar_Init(void);

/*
 * ========== 采样和计算函数声明 ==========
 */

/**
 * @brief 信号采样函数
 * @param 无
 * @return 无
 * @note 采集电压、电流等模拟信号，进行AD转换和标度变换
 */
void solar_Sampler(void);

/**
 * @brief 交流信号有效值计算函数
 * @param 无
 * @return 无
 * @note 计算三相电压电流的有效值，用于功率计算和保护判断
 */
void solar_AC_RMS_Calc(void);

/*
 * ========== 保护和管理函数声明 ==========
 */

/**
 * @brief 微秒级保护函数
 * @param 无
 * @return 无
 * @note 执行快速保护检测，如过流保护等
 */
void solar_Protector_us(void);

/**
 * @brief 毫秒级保护函数
 * @param 无
 * @return 无
 * @note 执行慢速保护检测，如过压、欠压、频率异常等
 */
void solar_Protector_ms(void);

/**
 * @brief 光伏管理函数
 * @param 无
 * @return 无
 * @note 管理光伏板的工作状态，包括MPPT控制和功率管理
 */
void solar_PV_Manager(void);

/**
 * @brief 状态管理函数
 * @param 无
 * @return 无
 * @note 管理系统状态机，控制启动、同步、并网等状态转换
 */
void solar_StateManager(void);

/**
 * @brief 通信管理函数
 * @param 无
 * @return 无
 * @note 处理与外部设备的通信，包括数据上传和指令接收
 */
void solar_Communicator(void);

/*
 * ========== 控制算法函数声明 ==========
 */

/**
 * @brief 电网电压幅值同步函数
 * @param PVB 光伏升压模块句柄指针
 * @return 无
 * @note 实现逆变器输出电压与电网电压的幅值同步，为并网做准备
 */
void solar_amp_sync(PVBoost_Handle_t *PVB);

/**
 * @brief MPPT最大功率点跟踪函数
 * @param 无
 * @return 无
 * @note 执行最大功率点跟踪算法，使光伏板工作在最佳功率点
 */
void solar_MPPT(void);

/**
 * @brief 控制器初始化函数
 * @param 无
 * @return 无
 * @note 初始化所有PI控制器参数
 */
void solar_cntl_init(void);

/**
 * @brief 控制器计算函数
 * @param 无
 * @return 无
 * @note 执行电流环、电压环等控制算法计算
 */
void solar_cntl_calc(void);

/**
 * @brief MPPT算法初始化函数
 * @param 无
 * @return 无
 * @note 初始化MPPT算法相关参数
 */
void solar_mppt_init(void);

/**
 * @brief MPPT算法计算函数
 * @param 无
 * @return 无
 * @note 执行MPPT算法计算，输出最优工作点电压
 */
void solar_mppt_calc(void);

/**
 * @brief 软件锁相环初始化函数
 * @param 无
 * @return 无
 * @note 初始化锁相环参数，用于电网同步
 */
void solar_spll_init(void);

/**
 * @brief 软件锁相环计算函数
 * @param 无
 * @return 无
 * @note 执行锁相环算法，跟踪电网频率和相位
 */
void solar_spll_calc(void);

/**
 * @brief PWM输出函数
 * @param 无
 * @return 无
 * @note 根据控制算法结果输出PWM信号，驱动逆变器开关管
 */
void solar_output_epwm(void);

/*
 * ========== 系统控制函数声明 ==========
 */

/**
 * @brief 系统关闭函数
 * @param 无
 * @return 无
 * @note 安全关闭系统，禁止所有输出
 */
void solar_off(void);

/**
 * @brief 升压电路使能函数
 * @param 无
 * @return 无
 * @note 启动DC-DC升压电路
 */
void solar_boost_enable(void);

/**
 * @brief 升压电路禁止函数
 * @param 无
 * @return 无
 * @note 关闭DC-DC升压电路
 */
void solar_boost_disable(void);

/**
 * @brief 逆变器PWM使能函数
 * @param 无
 * @return 无
 * @note 启动逆变器PWM输出
 */
void solar_inv_pwm_enable(void);

/**
 * @brief 逆变器PWM禁止函数
 * @param 无
 * @return 无
 * @note 关闭逆变器PWM输出
 */
void solar_inv_pwm_disable(void);

/*
 * ========== 宏定义 ==========
 */

// 获取并网检测信号状态（通过GPIO72引脚读取）
// 返回值：1表示允许并网，0表示禁止并网
#define SOLAR_GET_INTERCONNECTION() (GpioDataRegs.GPCDAT.bit.GPIO72)

/*
 * ========== 调试功能函数声明 ==========
 */
#if SOLAR_DEBUG
/**
 * @brief 调试功能初始化函数
 * @param 无
 * @return 无
 * @note 仅在调试模式下编译，初始化调试相关功能
 */
void solar_debug_init(void);
#endif

#endif  // 头文件保护结束
