#ifndef __SAMPLE_H__
#define __SAMPLE_H__

#include "common.h"


// 每周期采样次数
#define SAMPLES_1P (TIM2_SIN_PRD_CNT)

// X 周期数
#define SAMPLES_XP 10

// 指数移动平均滤波系数
#define AC_EMA_Multiplier (1.0f / 1.0f)
#define DC_EMA_Multiplier (1.0f / 1.0f)

// 交流电采样
typedef struct _Sample_AC_HandleTypeDef
{

    float out_rms_xp;  // 均方根值 SAMPLES_XP 个周期
    float out_rms_1p;  // 均方根值 一个周期
    float out_instant; // 瞬时值

    // instant
    float out_pu_value; // 归一值
    float calc_pu_base; // 归一化基准值
    float calc_pu_k;    // 归一化系数

    //
    float calc_slope;     // 斜率
    float calc_intercept; // 截距

    uint32_t in_adc_value; // adc 采样值
    float adc_value_f;     // adc 滤波后值

    uint32_t calc_adc_sum; // adc总和 用于计算ADC均值
    float adc_avg;         // adc均值 用于计算偏置

    uint32_t sample_count; // 采样计数
    float calc_sq_sum;     // 方差总和

    uint32_t calc_rms_xp_count; //
    float calc_rms_xp_sum;      //

    uint32_t flag_completed_1p;
    uint32_t flag_completed_xp;

    uint32_t adc_buf_idx;         // 采样值索引
    uint16_t adc_buf[SAMPLES_1P]; // 采样值

} Sample_AC_HandleTypeDef;

typedef struct _Sample_DC_HandleTypeDef
{
    float out_instant; // 瞬时值

    uint32_t in_adc_value; //

    float calc_adc_value_f; //
    float calc_slope;       // 斜率
    float calc_intercept;   // 截距

} Sample_DC_HandleTypeDef;

void Sample_Membe_Init(Sample_AC_HandleTypeDef *h, float pu_base, float calc_slope, float calc_intercept);
void Sample_AC_Calculate(Sample_AC_HandleTypeDef *hACS, uint16_t adc_value);

void Sample_DC_Init(Sample_DC_HandleTypeDef *h, float calc_slope, float calc_intercept);
void Sample_DC_Calculate(Sample_DC_HandleTypeDef *hDCS, uint16_t adc_value);

#define SAMPLE_AC_CALCULATE_MACRO(h, adc_v)                                           \
    {                                                                                 \
        h.in_adc_value = ( h.in_adc_value>>1)+ ((adc_v) >> 5);                                                \
        h.calc_adc_sum += h.in_adc_value;                                             \
                                                                                      \
        h.adc_value_f += ((float)h.in_adc_value - h.adc_value_f) * AC_EMA_Multiplier; \
        h.out_instant = (h.adc_value_f - h.adc_avg) * h.calc_slope;                   \
                                                                                      \
        h.calc_sq_sum += h.out_instant * h.out_instant;                               \
        h.sample_count++;                                                             \
                                                                                      \
        h.adc_buf[h.adc_buf_idx >> 1] = h.in_adc_value;                               \
        if (++h.adc_buf_idx >= SAMPLES_1P)                                            \
            h.adc_buf_idx = 0;                                                        \
    }

#define SAMPLE_AC_RMS_CALCULATE_MACRO(h, adc_v)                                               \
    {                                                                                         \
        if (h.sample_count >= SAMPLES_1P)                                                     \
        {                                                                                     \
            h.adc_avg = h.calc_adc_sum / h.sample_count;                                      \
                                                                                              \
            h.out_rms_1p = (sqrtf(h.calc_sq_sum / (float)h.sample_count)) + h.calc_intercept; \
            h.calc_sq_sum = 0, h.sample_count = 0, h.calc_adc_sum = 0;                        \
                                                                                              \
            h.flag_completed_1p = 1;                                                          \
                                                                                              \
            h.calc_rms_xp_sum += h.out_rms_1p;                                                \
            if (++h.calc_rms_xp_count >= SAMPLES_XP)                                          \
            {                                                                                 \
                h.calc_rms_xp_count = 0;                                                      \
                                                                                              \
                h.out_rms_xp = h.calc_rms_xp_sum / (float)SAMPLES_XP;                         \
                h.calc_rms_xp_sum = 0;                                                        \
                h.flag_completed_xp = 1;                                                      \
            }                                                                                 \
        }                                                                                     \
    }

#define SAMPLE_DC_CALCULATE_MACRO(h, adc_v)                                                     \
    {                                                                                           \
    h.in_adc_value = ( h.in_adc_value>>1)+ ((adc_v) >> 5);                                                \
                                                                                                \
        h.calc_adc_value_f += ((float)h.in_adc_value - h.calc_adc_value_f) * DC_EMA_Multiplier; \
                                                                                                \
        h.out_instant = h.calc_adc_value_f * h.calc_slope + h.calc_intercept;                   \
        if (h.out_instant < 0)                                                                  \
        {                                                                                       \
            h.out_instant = 0;                                                                  \
        }                                                                                       \
    }

#endif // __SAMPLE_H__
